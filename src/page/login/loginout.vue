<template>
  <div class="education-logout-container">
    <!-- 顶部系统信息栏 -->
    <div class="header-bar">
      <div class="system-info">
        <h1 class="system-title">学情预警系统</h1>
        <span class="system-subtitle">Educational Early Warning System</span>
      </div>
      <div class="current-time">
        <el-icon><Clock /></el-icon>
        <span>{{ currentTime }}</span>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="logout-card animate__animated animate__fadeInUp">
        <!-- 教育主题图标区域 -->
        <div class="education-icon-section">
          <div class="icon-wrapper animate__animated animate__bounceIn">
            <el-icon :size="100" color="#1890ff">
              <Reading />
            </el-icon>
          </div>
          <div class="success-badge animate__animated animate__zoomIn">
            <el-icon :size="24" color="#52c41a">
              <CircleCheckFilled />
            </el-icon>
          </div>
        </div>

        <!-- 退出状态信息 -->
        <div class="logout-status">
          <h2 class="status-title">退出登录成功</h2>
          <p class="status-message">您已安全退出学情预警系统，感谢您的使用</p>
        </div>

        <!-- 用户信息卡片 -->
        <div class="user-card" v-if="shouldShowUserCard">
          <div class="user-avatar">
            <el-icon :size="32" color="#1890ff">
              <User />
            </el-icon>
          </div>
          <div class="user-details">
            <p class="user-name">{{ getUserDisplayName() }}</p>
            <p class="logout-time">退出时间：{{ logoutTime }}</p>
          </div>
        </div>

        <!-- 安全提示信息 -->
        <div class="security-notice">
          <el-icon color="#fa8c16"><WarningFilled /></el-icon>
          <span>为保护您的账户安全，请及时关闭浏览器或清除缓存</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useStore } from "vuex";
import { Clock, Reading, CircleCheckFilled, User, WarningFilled } from "@element-plus/icons-vue";
import { getStore } from "@/utils/store";
import dayjs from "dayjs";

// 定义组件选项
defineOptions({
  name: "LoginLoginout",
});

// 组合式API实例
const store = useStore();

// 响应式数据定义
const currentTime = ref("");
const logoutTime = ref("");
const userInfo = ref(null);
let animationFrameId = null;
let lastUpdateTime = 0;

// 计算属性：判断是否应该显示用户信息卡片
const shouldShowUserCard = computed(() => {
  try {
    // 检查localStorage中是否有用户名
    const userName = localStorage.getItem("userName");
    return userName && userName.trim() !== "";
  } catch (error) {
    // 如果无法访问localStorage，不显示用户卡片
    return false;
  }
});

// 生命周期钩子
onMounted(() => {
  initializeLogoutPage();
  startTimeUpdater();
});

onUnmounted(() => {
  // 清理requestAnimationFrame
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
  }
});

// 页面初始化方法
const initializeLogoutPage = () => {
  // 尝试获取用户信息（退出前的信息）
  userInfo.value = getStore({ name: "userInfo" }) || null;

  // 记录退出时间
  logoutTime.value = dayjs().format("YYYY年MM月DD日 HH:mm:ss");

  // 确保用户状态已完全清除
  ensureUserLoggedOut();

  // 添加页面进入动画延迟
  setTimeout(() => {
    document.querySelector(".logout-card")?.classList.add("animate__animated", "animate__fadeInUp");
  }, 100);
};

// 现代化时间更新器 - 使用requestAnimationFrame API
const startTimeUpdater = () => {
  const updateCurrentTime = () => {
    // 获取当前时间并格式化，包含星期信息
    const now = dayjs();
    const weekdays = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
    const weekday = weekdays[now.day()];

    currentTime.value = `${now.format("YYYY年MM月DD日")} ${weekday} ${now.format("HH:mm:ss")}`;
  };

  // 使用requestAnimationFrame实现高性能时间更新
  const animateTime = (currentTimestamp) => {
    // 检查是否需要更新时间（每秒更新一次）
    if (currentTimestamp - lastUpdateTime >= 1000) {
      updateCurrentTime();
      lastUpdateTime = currentTimestamp;
    }

    // 继续下一帧动画
    animationFrameId = requestAnimationFrame(animateTime);
  };

  // 立即更新一次时间
  updateCurrentTime();
  lastUpdateTime = performance.now();

  // 启动动画循环
  animationFrameId = requestAnimationFrame(animateTime);
};

// 确保用户已退出登录
const ensureUserLoggedOut = () => {
  if (store.getters.token) {
    store.dispatch("FedLogOut").catch((error) => {
      console.warn("清除用户登录状态时出现错误:", error);
    });
  }
};

// 获取用户显示名称 - 从localStorage直接获取
const getUserDisplayName = () => {
  try {
    // 直接从localStorage获取用户名称
    const userName = localStorage.getItem("userName");

    // 如果localStorage中有用户名且不为空，则使用它
    if (userName && userName.trim() !== "") {
      return userName.trim();
    }

    // 如果localStorage中没有用户名，返回默认值
    return "用户";
  } catch (error) {
    // 处理localStorage访问异常（如隐私模式等）
    console.warn("无法访问localStorage获取用户名:", error);
    return "用户";
  }
};
</script>

<style lang="scss" scoped>
// 学情预警系统退出页面样式

.education-logout-container {
  min-height: 100vh; // 改回 min-height，允许内容超出时滚动
  background: linear-gradient(135deg, #4a90e2 0%, #6ba3e8 50%, #eceff1 100%);
  display: flex;
  flex-direction: column;
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  position: relative;
  overflow-x: hidden; // 只隐藏水平滚动条，允许垂直滚动

  // 教育主题背景装饰元素
  &::before {
    content: "";
    position: absolute;
    top: -20%;
    right: -20%;
    width: 40%;
    height: 40%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 50%, transparent 100%);
    border-radius: 50%;
    animation: gentleFloat 8s ease-in-out infinite;
  }

  &::after {
    content: "";
    position: absolute;
    bottom: -15%;
    left: -15%;
    width: 30%;
    height: 30%;
    background: radial-gradient(circle, rgba(74, 144, 226, 0.12) 0%, rgba(107, 163, 232, 0.06) 50%, transparent 100%);
    border-radius: 50%;
    animation: gentleFloat 10s ease-in-out infinite reverse;
  }
}

// 顶部系统信息栏 - 教育主题设计
.header-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 40px; // 减少上下 padding 从 25px 到 15px
  background: rgba(255, 255, 255, 0.18);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 10;
  position: relative;
  box-shadow: 0 4px 20px rgba(74, 144, 226, 0.15);
  flex-shrink: 0; // 防止头部被压缩

  .system-info {
    .system-title {
      color: #fff;
      font-size: 24px; // 减少字体大小从 28px 到 24px
      font-weight: 700;
      margin: 0 0 5px 0; // 减少下边距从 8px 到 5px
      text-shadow: 0 2px 8px rgba(55, 71, 79, 0.3);
      letter-spacing: 1px;
    }

    .system-subtitle {
      color: rgba(255, 255, 255, 0.9);
      font-size: 14px; // 减少字体大小从 15px 到 14px
      font-style: italic;
      font-weight: 300;
      letter-spacing: 0.5px;
    }
  }

  .current-time {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #fff;
    font-size: 17px;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.15);
    padding: 12px 20px;
    border-radius: 25px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);

    .el-icon {
      font-size: 20px;
    }
  }
}

// 主要内容区域 - 智能响应式布局
.main-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  position: relative;
  z-index: 5;
  min-height: 0; // 允许flex子项收缩

  // 当屏幕高度不足时，改为顶部对齐并允许滚动
  @media (max-height: 700px) {
    align-items: flex-start;
    overflow-y: auto;
    padding: 10px 20px 20px;
  }

  // 极小高度时进一步优化
  @media (max-height: 600px) {
    padding: 5px 15px 15px;
  }
}

// 退出登录卡片 - 智能响应式设计
.logout-card {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(30px);
  border-radius: 32px;
  padding: 40px 50px 35px;
  box-shadow:
    0 25px 80px rgba(55, 71, 79, 0.12),
    0 10px 40px rgba(74, 144, 226, 0.18);
  text-align: center;
  max-width: 480px;
  width: 100%;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.5);
  transform: translateY(0);
  transition: all 0.3s ease;

  // 根据屏幕高度动态调整卡片样式
  @media (max-height: 700px) {
    padding: 25px 40px 25px;
    border-radius: 24px;
    max-width: 450px;
  }

  @media (max-height: 600px) {
    padding: 20px 30px 20px;
    border-radius: 20px;
    max-width: 420px;
  }

  @media (max-height: 500px) {
    padding: 15px 25px 15px;
    border-radius: 16px;
    max-width: 400px;
  }

  // 悬浮效果
  &:hover {
    transform: translateY(-5px);
    box-shadow:
      0 35px 100px rgba(55, 71, 79, 0.15),
      0 15px 50px rgba(74, 144, 226, 0.25);
  }
}

// 教育主题图标区域 - 智能响应式设计
.education-icon-section {
  position: relative;
  margin-bottom: 30px;

  // 根据屏幕高度动态调整间距
  @media (max-height: 700px) {
    margin-bottom: 20px;
  }

  @media (max-height: 600px) {
    margin-bottom: 15px;
  }

  @media (max-height: 500px) {
    margin-bottom: 10px;
  }

  .icon-wrapper {
    position: relative;
    display: inline-block;
    padding: 20px;
    background: linear-gradient(135deg, #eceff1 0%, #f5f5f5 50%, #fafafa 100%);
    border-radius: 50%;
    box-shadow:
      0 15px 35px rgba(74, 144, 226, 0.15),
      0 5px 15px rgba(53, 122, 189, 0.1);
    border: 3px solid rgba(255, 255, 255, 0.95);
    transition: all 0.3s ease;

    // 根据屏幕高度动态调整图标包装器
    @media (max-height: 700px) {
      padding: 15px;
    }

    @media (max-height: 600px) {
      padding: 12px;
    }

    @media (max-height: 500px) {
      padding: 10px;
    }

    &:hover {
      transform: scale(1.05);
      box-shadow:
        0 20px 45px rgba(74, 144, 226, 0.2),
        0 8px 20px rgba(53, 122, 189, 0.15);
    }

    .el-icon {
      color: #4a90e2 !important;
      font-size: 100px !important;

      // 根据屏幕高度动态调整图标大小
      @media (max-height: 700px) {
        font-size: 80px !important;
      }

      @media (max-height: 600px) {
        font-size: 70px !important;
      }

      @media (max-height: 500px) {
        font-size: 60px !important;
      }
    }
  }

  .success-badge {
    position: absolute;
    bottom: 15px;
    right: 15px;
    background: linear-gradient(135deg, #fff, #f8f9fa);
    border-radius: 50%;
    padding: 10px;
    box-shadow:
      0 6px 20px rgba(124, 179, 66, 0.2),
      0 2px 8px rgba(0, 0, 0, 0.06);
    animation-delay: 1s;
    border: 2px solid rgba(124, 179, 66, 0.1);

    .el-icon {
      color: #7cb342 !important;
    }
  }
}

// 退出状态信息 - 智能响应式排版
.logout-status {
  margin-bottom: 30px;

  // 根据屏幕高度动态调整间距
  @media (max-height: 700px) {
    margin-bottom: 20px;
  }

  @media (max-height: 600px) {
    margin-bottom: 15px;
  }

  @media (max-height: 500px) {
    margin-bottom: 10px;
  }

  .status-title {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 30px;
    font-weight: 800;
    margin: 0 0 15px 0;
    text-shadow: 0 4px 8px rgba(74, 144, 226, 0.1);
    letter-spacing: 1px;

    // 根据屏幕高度动态调整标题
    @media (max-height: 700px) {
      font-size: 26px;
      margin: 0 0 12px 0;
    }

    @media (max-height: 600px) {
      font-size: 24px;
      margin: 0 0 10px 0;
    }

    @media (max-height: 500px) {
      font-size: 22px;
      margin: 0 0 8px 0;
    }
  }

  .status-message {
    color: #37474f;
    font-size: 16px;
    line-height: 1.6;
    margin: 0;
    font-weight: 400;
    opacity: 0.85;

    // 根据屏幕高度动态调整消息文本
    @media (max-height: 700px) {
      font-size: 15px;
      line-height: 1.5;
    }

    @media (max-height: 600px) {
      font-size: 14px;
      line-height: 1.4;
    }

    @media (max-height: 500px) {
      font-size: 13px;
      line-height: 1.3;
    }
  }
}

// 用户信息卡片 - 智能响应式设计
.user-card {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 50%, #eceff1 100%);
  border-radius: 20px;
  padding: 20px;
  margin: 25px 0;
  border: 1px solid rgba(74, 144, 226, 0.1);
  box-shadow:
    0 8px 25px rgba(74, 144, 226, 0.08),
    0 3px 10px rgba(55, 71, 79, 0.04);
  position: relative;
  overflow: hidden;

  // 根据屏幕高度动态调整用户卡片
  @media (max-height: 700px) {
    padding: 15px;
    margin: 18px 0;
    border-radius: 16px;
  }

  @media (max-height: 600px) {
    padding: 12px;
    margin: 15px 0;
    border-radius: 14px;
  }

  @media (max-height: 500px) {
    padding: 10px;
    margin: 12px 0;
    border-radius: 12px;
  }

  // 教育主题装饰条
  &::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 5px;
    background: linear-gradient(135deg, #4a90e2, #7cb342);
  }

  .user-avatar {
    background: linear-gradient(135deg, #fff, #f8f9fa);
    border-radius: 50%;
    padding: 12px; // 减少 padding 从16px到12px
    margin-right: 20px; // 减少右边距从25px到20px
    box-shadow:
      0 6px 20px rgba(74, 144, 226, 0.12),
      0 2px 8px rgba(55, 71, 79, 0.06);
    border: 2px solid rgba(74, 144, 226, 0.08);

    .el-icon {
      color: #4a90e2 !important;
    }
  }

  .user-details {
    flex: 1;
    text-align: left;

    .user-name {
      background: linear-gradient(135deg, #4a90e2, #357abd);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-size: 18px; // 减少字体大小从20px到18px
      font-weight: 700;
      margin: 0 0 8px 0; // 减少下边距从10px到8px
      letter-spacing: 0.5px;
    }

    .logout-time {
      color: #78909c;
      font-size: 14px; // 减少字体大小从15px到14px
      margin: 0;
      font-weight: 500;
      opacity: 0.9;
    }
  }
}

// 安全提示信息 - 智能响应式警告设计
.security-notice {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background: linear-gradient(135deg, #fff8e1 0%, #fff3c4 50%, #ffecb3 100%);
  border: 1px solid rgba(255, 152, 0, 0.3);
  border-radius: 16px;
  padding: 15px 20px;
  margin-top: 25px;
  color: #e65100;
  font-size: 14px;
  line-height: 1.5;
  font-weight: 500;
  box-shadow:
    0 4px 15px rgba(255, 152, 0, 0.1),
    0 2px 8px rgba(55, 71, 79, 0.04);
  position: relative;
  overflow: hidden;

  // 根据屏幕高度动态调整安全提示
  @media (max-height: 700px) {
    padding: 12px 16px;
    margin-top: 18px;
    font-size: 13px;
    gap: 8px;
    border-radius: 14px;
  }

  @media (max-height: 600px) {
    padding: 10px 14px;
    margin-top: 15px;
    font-size: 12px;
    gap: 6px;
    border-radius: 12px;
  }

  @media (max-height: 500px) {
    padding: 8px 12px;
    margin-top: 12px;
    font-size: 11px;
    gap: 5px;
    border-radius: 10px;
    line-height: 1.4;
  }

  // 装饰性光晕效果
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 3s infinite;
  }

  .el-icon {
    font-size: 18px;
    flex-shrink: 0;
    color: #ff9800 !important;
    filter: drop-shadow(0 2px 4px rgba(255, 152, 0, 0.2));
  }
}

// 动画定义 - 更加优雅的动画效果
@keyframes gentleFloat {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg) scale(1);
  }
  50% {
    transform: translateY(-15px) rotate(3deg) scale(1.02);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

// 响应式设计 - 优化移除按钮后的布局
@media (max-width: 768px) {
  .header-bar {
    padding: 12px 20px; // 进一步减少移动端头部 padding
    flex-direction: column;
    gap: 10px; // 减少间距从15px到10px
    text-align: center;

    .system-info .system-title {
      font-size: 24px;
    }

    .system-info .system-subtitle {
      font-size: 13px;
    }

    .current-time {
      font-size: 15px;
      padding: 10px 18px;
    }
  }

  .main-content {
    padding: 15px; // 进一步减少移动端主内容区域 padding
  }

  .logout-card {
    padding: 35px 30px 30px; // 减少移动端卡片 padding
    border-radius: 28px;
    margin: 0 10px;

    .education-icon-section {
      margin-bottom: 25px; // 减少移动端图标区域下边距

      .icon-wrapper {
        padding: 18px; // 减少移动端图标包装器 padding

        .el-icon {
          font-size: 80px !important; // 减少移动端图标大小
        }
      }

      .success-badge {
        bottom: 12px;
        right: 12px;
        padding: 8px;
      }
    }

    .logout-status {
      margin-bottom: 25px; // 减少移动端状态信息下边距

      .status-title {
        font-size: 26px; // 减少移动端标题字体大小
      }

      .status-message {
        font-size: 15px; // 减少移动端消息字体大小
      }
    }

    .user-card {
      flex-direction: column;
      text-align: center;
      gap: 20px;
      padding: 25px;

      .user-avatar {
        margin-right: 0;
        padding: 14px;
      }

      .user-details {
        text-align: center;

        .user-name {
          font-size: 18px;
        }

        .logout-time {
          font-size: 14px;
        }
      }
    }

    .security-notice {
      padding: 18px 22px;
      font-size: 14px;
      margin-top: 35px;
    }
  }
}

@media (max-width: 480px) {
  .header-bar {
    padding: 18px 20px;

    .system-info .system-title {
      font-size: 22px;
    }

    .current-time {
      font-size: 14px;
      padding: 8px 15px;
    }
  }

  .main-content {
    padding: 30px 15px;
  }

  .logout-card {
    padding: 50px 30px 40px;
    margin: 0 5px;
    border-radius: 24px;

    .education-icon-section {
      margin-bottom: 35px;

      .icon-wrapper {
        padding: 20px;

        .el-icon {
          font-size: 70px !important;
        }
      }

      .success-badge {
        bottom: 8px;
        right: 8px;
        padding: 6px;
      }
    }

    .logout-status {
      margin-bottom: 35px;

      .status-title {
        font-size: 24px;
      }

      .status-message {
        font-size: 15px;
      }
    }

    .user-card {
      padding: 20px;
      gap: 15px;

      .user-avatar {
        padding: 12px;
      }

      .user-details {
        .user-name {
          font-size: 16px;
        }

        .logout-time {
          font-size: 13px;
        }
      }
    }

    .security-notice {
      padding: 15px 18px;
      font-size: 13px;
      margin-top: 30px;
    }
  }
}

// 极小屏幕高度的特殊处理 - 确保内容完整显示
@media (max-height: 450px) {
  .education-logout-container {
    padding: 5px 0;
  }

  .header-bar {
    padding: 8px 20px;

    .system-info .system-title {
      font-size: 18px;
      margin: 0 0 3px 0;
    }

    .system-info .system-subtitle {
      font-size: 11px;
    }

    .current-time {
      font-size: 12px;
      padding: 6px 12px;
    }
  }

  .main-content {
    padding: 5px 10px 10px;
  }

  .logout-card {
    padding: 10px 20px 10px;
    border-radius: 12px;
    max-width: 380px;

    .education-icon-section {
      margin-bottom: 8px;

      .icon-wrapper {
        padding: 8px;

        .el-icon {
          font-size: 50px !important;
        }
      }

      .success-badge {
        padding: 4px;
        bottom: 5px;
        right: 5px;

        .el-icon {
          font-size: 16px !important;
        }
      }
    }

    .logout-status {
      margin-bottom: 8px;

      .status-title {
        font-size: 20px;
        margin: 0 0 6px 0;
      }

      .status-message {
        font-size: 12px;
        line-height: 1.3;
      }
    }

    .user-card {
      padding: 8px;
      margin: 10px 0;
      border-radius: 10px;

      .user-avatar {
        padding: 8px;
        margin-right: 12px;

        .el-icon {
          font-size: 24px !important;
        }
      }

      .user-details {
        .user-name {
          font-size: 14px;
          margin: 0 0 4px 0;
        }

        .logout-time {
          font-size: 11px;
        }
      }
    }

    .security-notice {
      padding: 6px 10px;
      margin-top: 10px;
      font-size: 10px;
      gap: 4px;
      border-radius: 8px;
      line-height: 1.3;

      .el-icon {
        font-size: 14px !important;
      }
    }
  }
}

// 教育系统专用浅色主题设计
// 系统仅支持浅色模式，确保在所有环境下保持一致的专业外观和用户体验
</style>
