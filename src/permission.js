import router from "./router/";
import store from "./store";
import { getToken } from "@/utils/auth";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { ElLoading, ElMessageBox } from "element-plus";
import { baseUrl } from "@/config/env";
import website from "@/config/website";

// 配置进度条
NProgress.configure({ showSpinner: false });

// 常量定义
const LOCK_PAGE = "/lock";
const LOGIN_PAGE = "/login";
const FORBIDDEN_PAGE = "/403";
const HOME_PAGE = "/";

/**
 * CAS登录处理函数
 * @param {Object} to - 目标路由对象
 * @returns {Promise<string|boolean>} 重定向路径或false
 */
const handleCasLogin = async (to) => {
  // 如果不是CAS回调且启用了CAS登录，执行CAS登录流程
  if (!to.query?.cas && globalThis.__CAS_LOGIN) {
    const loading = ElLoading.service({
      lock: true,
      text: "登录中,请稍后",
      background: "rgba(0, 0, 0, 0.7)",
    });

    try {
      await Promise.all([store.dispatch("LoginByCas"), store.dispatch("FlowRoutes")]);
      return store.getters.tagWel || HOME_PAGE;
    } catch (error) {
      console.error("CAS登录失败:", error);
      ElMessageBox.confirm("登录失败，请重新登录", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        window.location.href = `${baseUrl}${website.casLoginUrl}`;
      });
      return false;
    } finally {
      loading.close();
    }
  }

  return LOGIN_PAGE;
};
/**
 * 设置路由组件名称
 * @param {Object} to - 目标路由对象
 */
const setRouteComponentName = (to) => {
  const lastMatchedRoute = to.matched?.at(-1);
  const component = lastMatchedRoute?.components?.default;

  if (typeof component === "function") {
    component().then((mod) => {
      if (mod?.default) {
        mod.default.name = to.fullPath;
      }
    });
  }
};

/**
 * 处理菜单加载
 * @param {Object} to - 目标路由对象
 * @param {Function} next - 路由跳转函数
 */
const handleMenuLoading = async (to, next) => {
  try {
    const menuData = await store.dispatch("GetMenu");
    if (menuData?.length > 0) {
      router.$avueRouter.formatRoutes(menuData, true);
      next(to);
    }
  } catch (error) {
    console.error("菜单加载失败:", error);
    next(LOGIN_PAGE);
  }
};

/**
 * 处理用户登出并重新登录
 * @param {Object} to - 目标路由对象
 * @param {Function} next - 路由跳转函数
 */
const handleLogoutAndRelogin = async (to, next) => {
  try {
    await store.dispatch("FedLogOut");
    const redirectPath = await handleCasLogin(to);
    next(redirectPath);
  } catch (error) {
    console.error("登出和重新登录失败:", error);
    next(LOGIN_PAGE);
  }
};

/**
 * 处理标签页添加
 * @param {Object} to - 目标路由对象
 */
const handleTabAddition = (to) => {
  const { meta = {}, query = {} } = to;

  // 如果是外部链接，直接打开
  if (meta.target) {
    window.open(query.url?.replace(/#/g, "&"));
    return true;
  }

  // 添加标签页
  if (meta.isTab !== false && meta.isNewTab !== 1) {
    store.commit("ADD_TAG", {
      name: query.name || to.name,
      path: to.path,
      fullPath: to.path,
      params: to.params,
      query: to.query,
      meta,
    });
  }

  return false;
};

/**
 * 检查是否需要认证
 * @param {Object} to - 目标路由对象
 * @returns {boolean} 是否需要认证
 */
const needsAuthentication = (to) => {
  const { meta = {} } = to;

  // 特殊情况：登录页面的处理
  if (to.path === LOGIN_PAGE) {
    const isCasEnabled = globalThis.__CAS_LOGIN;
    const isCasCallback = to.query?.cas;

    // 如果启用了CAS且不是CAS回调，则需要认证（会触发CAS登录流程）
    // 如果未启用CAS或者是CAS回调，则不需要认证（直接显示登录页）
    return isCasEnabled && !isCasCallback;
  }
  // 如果路由明确标记为不需要认证，则直接返回false
  if (meta.isAuth === false) {
    return false;
  }
  // 其他所有页面默认都需要认证
  return true;
};

// 路由前置守卫
router.beforeEach(async (to, _from, next) => {
  // 设置路由组件名称
  setRouteComponentName(to);

  // 设置菜单显示状态
  const { meta = {}, query = {} } = to;
  const isMenu = meta.menu ?? query.menu;
  store.commit("SET_IS_MENU", isMenu === undefined);

  // 检查是否已登录
  const hasToken = !!getToken();

  if (hasToken) {
    // 已登录用户的路由处理
    const { isLock, permission, menu, token } = store.getters;

    // 锁屏检查
    if (isLock && to.path !== LOCK_PAGE) {
      return next({ path: LOCK_PAGE });
    }

    // 权限检查
    if (meta.permission && !permission?.[meta.permission]) {
      return next({ path: FORBIDDEN_PAGE });
    }

    // 登录页重定向
    if (to.path === LOGIN_PAGE) {
      return next({ path: HOME_PAGE });
    }

    // 菜单加载检查
    if (Array.isArray(menu) && menu.length === 0) {
      return handleMenuLoading(to, next);
    }

    // Token有效性检查
    if (!token?.length) {
      return handleLogoutAndRelogin(to, next);
    }

    // 处理标签页和外部链接
    if (handleTabAddition(to)) {
      return; // 外部链接已处理，直接返回
    }

    next();
  } else {
    // 未登录用户的路由处理
    if (!needsAuthentication(to)) {
      return next();
    }
    try {
      const redirectPath = await handleCasLogin(to);
      next(redirectPath);
    } catch (error) {
      console.error("CAS登录失败:", error);
      next(LOGIN_PAGE);
    }
  }
});

// 路由后置守卫
router.afterEach((to) => {
  // 完成进度条
  NProgress.done();

  // 设置页面标题
  const title = router.$avueRouter.generateTitle(to, { label: "name" });
  router.$avueRouter.setTitle(title);

  // 关闭搜索状态
  store.commit("SET_IS_SEARCH", false);
});
